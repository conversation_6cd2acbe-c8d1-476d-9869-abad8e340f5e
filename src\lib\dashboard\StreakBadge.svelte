<script lang="ts">
	import { streakData } from '../missions/mission.store.ts';
	import { H4, P2, P3 } from '../ui/index.js';

	function getStreakMessage(streak: number): string {
		if (streak === 0) return "Start your streak today!";
		if (streak === 1) return "Great start!";
		if (streak < 7) return "Building momentum!";
		if (streak < 30) return "On fire! 🔥";
		if (streak < 100) return "Incredible dedication!";
		return "Legendary streak! 🏆";
	}

	function getStreakColor(streak: number): string {
		if (streak === 0) return "var(--charcoal)";
		if (streak < 7) return "var(--tangerine)";
		if (streak < 30) return "var(--rose)";
		return "var(--purple)";
	}

	function getStreakBgClass(streak: number): string {
		if (streak === 0) return "streak-zero";
		if (streak < 7) return "streak-low";
		if (streak < 30) return "streak-medium";
		return "streak-high";
	}

	function getMilestoneReward(streak: number): string | null {
		if (streak === 7) return "7-day milestone! 🎉";
		if (streak === 30) return "30-day milestone! 🏅";
		if (streak === 100) return "100-day milestone! 👑";
		if (streak > 0 && streak % 50 === 0) return `${streak}-day milestone! ⭐`;
		return null;
	}
</script>

<div class="streak-badge-card">
	{#if $streakData}
		<div class="streak-content">
			<div class="icon-container">
				<div class="streak-icon">
					<!-- Fire icon for active streaks -->
					{#if $streakData.currentStreak > 0}
						<svg class="fire-icon" fill="currentColor" viewBox="0 0 20 20" style="color: {getStreakColor($streakData.currentStreak)}">
							<path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd" />
						</svg>
					{:else}
						<!-- Calendar icon for zero streak -->
						<svg class="calendar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
						</svg>
					{/if}
				</div>
			</div>

			<H4>Current Streak</H4>

			<div class="streak-number">
				<span class="number" style="color: {getStreakColor($streakData.currentStreak)}">
					{$streakData.currentStreak}
				</span>
			</div>

			<P3 isBold={true} --text-color={getStreakColor($streakData.currentStreak)}>
				{getStreakMessage($streakData.currentStreak)}
			</P3>

			<!-- Milestone celebration -->
			{#if getMilestoneReward($streakData.currentStreak)}
				<div class="milestone-badge {getStreakBgClass($streakData.currentStreak)}">
					<P2 isBold --text-color={getStreakColor($streakData.currentStreak)}>
						{getMilestoneReward($streakData.currentStreak)}
					</P2>
				</div>
			{/if}

			<!-- Best streak display -->
			{#if $streakData.bestStreak > 0}
				<div class="best-streak">
					<div class="best-streak-content">
						<svg class="star-icon" fill="currentColor" viewBox="0 0 20 20">
							<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
						</svg>
						<P2>Best: {$streakData.bestStreak} {$streakData.bestStreak === 1 ? 'day' : 'days'}</P2>
					</div>
				</div>
			{/if}

			<!-- Motivational tips -->
			{#if $streakData.currentStreak === 0}
				<div class="tip-card tip-start">
					<P3 --text-color="var(--sky-blue)">
						💡 Complete all daily missions to start your streak!
					</P3>
				</div>
			{:else if $streakData.currentStreak < 7}
				<div class="tip-card tip-progress">
					<P3 --text-color="var(--tangerine)">
						🎯 Keep going! {7 - $streakData.currentStreak} more {7 - $streakData.currentStreak === 1 ? 'day' : 'days'} to reach your first week!
					</P3>
				</div>
			{/if}
		</div>
	{:else}
		<div class="loading-state">
			<div class="loading-skeleton">
				<div class="skeleton-icon"></div>
				<div class="skeleton-text-small"></div>
				<div class="skeleton-text-large"></div>
			</div>
		</div>
	{/if}
</div>

<style>
	.streak-badge-card {
		background-color: var(--white);
		border: 2px solid var(--pitch-black);
		border-radius: 1rem;
		padding: 1.5rem;
		width: 100%;
		height: 100%;
	}

	.streak-content {
		text-align: center;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 1rem;
		height: 100%;
	}

	.icon-container {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.streak-icon {
		position: relative;
	}

	.fire-icon {
		width: 3rem;
		height: 3rem;
	}

	.calendar-icon {
		width: 3rem;
		height: 3rem;
		color: var(--charcoal);
		opacity: 0.6;
	}

	.number {
		font-size: 2.5rem;
		font-weight: 700;
		font-family: "Inter";
		line-height: 1;
	}

	.milestone-badge {
		padding: 0.75rem;
		border: 1px solid var(--pitch-black);
		border-radius: 0.5rem;
		margin: 1rem 0;
	}

	.milestone-badge.streak-zero {
		background-color: var(--very-light-sky-blue);
		border-color: var(--light-sky-blue);
	}

	.milestone-badge.streak-low {
		background-color: var(--light-tangerine);
		border-color: var(--tangerine);
	}

	.milestone-badge.streak-medium {
		background-color: var(--light-rose);
		border-color: var(--rose);
	}

	.milestone-badge.streak-high {
		background-color: var(--light-purple);
		border-color: var(--purple);
	}

	.best-streak-content {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
	}

	.star-icon {
		width: 1rem;
		height: 1rem;
		color: var(--yellow);
	}

	.tip-card {
		padding: 0.75rem;
		border: 1px solid var(--color);
		border-radius: 0.5rem;
		box-shadow: 0.25rem 0.25rem 0 var(--color);
	}

	.tip-card.tip-start {
		background-color: var(--light-sky-blue);
		--color: var(--sky-blue);
	}

	.tip-card.tip-progress {
		background-color: var(--light-tangerine);
		--color: var(--tangerine);
	}

	.loading-state {
		text-align: center;
		padding: 1rem 0;
	}

	.loading-skeleton {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 1rem;
	}

	.skeleton-icon {
		width: 3rem;
		height: 3rem;
		background-color: var(--light-sky-blue);
		border-radius: 50%;
		animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	}

	.skeleton-text-small {
		height: 1rem;
		width: 6rem;
		background-color: var(--light-sky-blue);
		border-radius: 0.25rem;
		animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	}

	.skeleton-text-large {
		height: 2rem;
		width: 4rem;
		background-color: var(--light-sky-blue);
		border-radius: 0.25rem;
		animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
	}

	@keyframes pulse {
		0%, 100% {
			opacity: 1;
		}
		50% {
			opacity: 0.5;
		}
	}
</style>
