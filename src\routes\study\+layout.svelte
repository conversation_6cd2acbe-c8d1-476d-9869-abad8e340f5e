<script lang="ts">
	import { browser } from "$app/environment";
    import { page } from "$app/state";
	import { getAuthContext } from "$lib/firebase/auth.svelte.ts";
    import { missionProgress, missionLoading, incrementMissionProgress, subscribeMissionProgress, subscribeStreakData } from '$lib/missions/index.ts';
    import NavBar from "$lib/study/NavBar.svelte";
	import { MissionMetric } from "$lib/types/mission.types.ts";
	import { onMount } from "svelte";
	import { innerWidth } from "svelte/reactivity/window";

    let { children, data } = $props();

    let currentPath = $derived(page.url.pathname);
    let currentHour = $derived(new Date().getHours());
    const { user, loading } = getAuthContext();

    // Check login missions when user is authenticated AND mission data is ready
    $effect(() => {
        if (user && !$missionLoading && $missionProgress !== null) {
            // Check morning login (9 AM - 4 PM)
            if (currentHour >= 9 && currentHour < 16 && !$missionProgress.missions[MissionMetric.MORNING_LOGIN]) {
                incrementMissionProgress(user.uid, MissionMetric.MORNING_LOGIN);
            }

            // Check evening login (7 PM - 11 PM)
            if (currentHour >= 19 && currentHour < 23 && !$missionProgress.missions[MissionMetric.EVENING_LOGIN]) {
                incrementMissionProgress(user.uid, MissionMetric.EVENING_LOGIN);
            }
        }
    });

    // Mission subscriptions
    onMount(() => {
        const missionUnsubscribe = subscribeMissionProgress(user.uid);
        const streakUnsubscribe = subscribeStreakData(user.uid);

        return () => {
            missionUnsubscribe();
            streakUnsubscribe();
        };
    });
</script>

<NavBar {currentPath} role={data.role}/>

<div class="children-container" class:mobile={innerWidth.current < 1024}>
    {@render children()}
</div>

<style>
    .children-container {
        margin: 0 auto;
        margin-left: 4.75rem;
    }

    .mobile {
        margin-left: 0;
        margin-top: 4rem; /* Account for fixed mobile top navbar */
    }
</style>

