<script lang="ts">
    import '../app.css';
    import { onNavigate } from '$app/navigation';
	import { page } from '$app/state';
    import { auth } from '$lib/firebase';
	import posthog from 'posthog-js';
	import { onMount, setContext, type Snippet } from 'svelte';
	import { onAuthStateChanged } from 'firebase/auth';
	import { browser } from '$app/environment';
	import { missionError, missionLoading, missionProgress, streakData } from '$lib/missions';
	import type { LayoutData } from './$types';

    let { children, data }: { children: Snippet, data: LayoutData } = $props();

    onNavigate((navigation) => {
        if (!document.startViewTransition) return;

        return new Promise((resolve) => {
            document.startViewTransition(async () => {
                resolve();
                await navigation.complete;
            });
        });
    });
</script>

{@render children?.()}