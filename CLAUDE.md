# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Architecture

### Tech Stack
- **Frontend**: SvelteKit 5 with TypeScript, Tailwind CSS
- **Backend Services**: Firebase (Auth, Firestore), Supabase, Stripe, Contentful CMS (Deprecated), Upstash Redis
- **AI Integration**: Google Generative AI for vocabulary features
- **Analytics**: PostHog for user tracking and analytics
- **Deployment**: Firebase hosting with serverless backend (asia-southeast1)

### Route Structure
The application uses SvelteKit's file-based routing with several key route groups:

- **`(marketing)/`**: Public marketing pages (landing page, pricing)
- **`study/`**: Protected study dashboard and tools (requires Pro role)
- **`bootcamp/`**: Bootcamp content (requires Bootcamp or Pro role)
- **`api/`**: Server-side API endpoints
- **`share-analysis/`**: Public analysis sharing functionality

### Authentication & Authorization
- Firebase Authentication handles user login/signup
- Custom claims system manages user roles: "<PERSON>", "Bootcamp", "Pro+Bootcamp"
- Server-side authentication implemented in `src/hooks.server.ts`
- Role-based access control enforced at the route level
- Session cookies used for server-side auth verification

### Key Architecture Patterns

**State Management:**
- Svelte 5 runes for reactive state (`$state`, `$derived`, `$effect`)
- Firebase auth state managed in `src/lib/firebase/auth.svelte.ts`
- User data persisted in Firestore with real-time listeners for role updates

**Database Architecture:**
- Firebase Firestore for user data, test results, and analytics
- Supabase for question banks and structured content
- Contentful CMS for bootcamp lectures and study materials (Trying to migrate to Supabase and Sanity)
- Redis (Upstash) for caching and rate limiting

**Content Management:**
- Questions and test data stored in Supabase
- Bootcamp content (lectures, notes, resources) managed via Contentful
- Static assets served from Firebase hosting

### Key Components & Features

**Mock Test System:**
- Adaptive testing simulation with real-time scoring
- Question types: Multiple choice, math (with calculator), verbal reasoning
- Timer management and section-based testing
- Detailed performance analysis and score prediction

**Study Tools:**
- Vocabulary learning with spaced repetition (ts-fsrs)
- Question bank practice with progress tracking
- Performance analytics and study plan generation

**Subscription System:**
- Stripe integration for payment processing
- Webhook handling for subscription events
- Role-based feature access control

**Daily Mission System:**
- Gamified user engagement with daily challenges
- Progress tracking in Firestore with real-time updates
- Streak management with milestone detection
- PostHog integration for mission completion analytics
- Transaction-based progress increments to prevent race conditions

## Important File Locations

- **Firebase Config**: `src/lib/firebase/config.ts` (hardcoded client config)
- **Server Services**: `src/lib/server/` (admin, contentful, supabase, redis, AI)
- **Type Definitions**: `src/lib/types/` (question types, vocab types, mission types, database types)
- **UI Components**: `src/lib/ui/` (reusable components)
- **Feature Modules**: `src/lib/analysis/`, `src/lib/mockTest/`, `src/lib/vocabTool/`, `src/lib/missions/`
- **Mission System**: `src/lib/missions/missionEngine.ts` (core logic), `src/lib/missions/missionCatalog.ts` (definitions)

## Testing Architecture

- **Framework**: Vitest with dual project setup
- **Client Tests**: Browser environment using Playwright/Chromium for Svelte component testing
- **Server Tests**: Node environment for server-side logic testing
- **Test Patterns**: Client tests use `*.svelte.{test,spec}.{js,ts}`, server tests use `*.{test,spec}.{js,ts}`
- **Configuration**: Separate test configurations in `vite.config.ts` with different environments and exclusions

## Environment & Configuration

- Firebase project configured for asia-southeast1 region
- Environment variables handled via SvelteKit's standard approach
- Client-side Firebase config is hardcoded (consider env vars for production)
- PostHog analytics integrated for user tracking and error monitoring
- **Mission Tracking**: Automatic mission completion events, streak milestone detection, and user property updates
- **Analytics Events**: `mission_completed`, `streak_milestone_reached` with detailed metadata
- **User Properties**: Tracks `current_streak`, `best_streak`, `total_missions_completed`

## Development Guidelines

- Follow existing Svelte 5 patterns with runes
- Maintain TypeScript strict mode compliance
- Use Tailwind for styling with custom color variables
- Implement proper error handling with PostHog error tracking
- Follow role-based access patterns for new features
- Test authentication flows with different user roles

## Important Instructions

Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.