import { doc, getDoc, setDoc, runTransaction } from 'firebase/firestore';
import { db } from '../firebase/firestore.js';
import { type DailyMissionProgress, type UserStreakData, MissionMetric } from '../types/mission.types.ts';
import { MISSION_CATALOG, getDailyMissions } from './missionCatalog.ts';
import posthog from 'posthog-js';
import { browser } from '$app/environment';

/**
 * Get today's date key in YYYY-MM-DD format
 */
export function getTodayKey(): string {
	return new Date().toISOString().split('T')[0];
}

/**
 * Get this week's key in YYYY-WW format
 */
export function getWeekKey(): string {
	const now = new Date();
	const year = now.getFullYear();
	const week = Math.ceil(((now.getTime() - new Date(year, 0, 1).getTime()) / 86400000 + new Date(year, 0, 1).getDay() + 1) / 7);
	return `${year}-${week.toString().padStart(2, '0')}`;
}

/**
 * Initialize daily progress document for today
 */
export async function initializeDailyProgress(userId: string): Promise<DailyMissionProgress> {
	const todayKey = getTodayKey();
	const dailyMissions = getDailyMissions();
	
	const initialProgress: DailyMissionProgress = {
		missions: {},
		completed: false,
		createdAt: new Date()
	};

	// Initialize all daily missions with 0 progress
	dailyMissions.forEach(mission => {
		initialProgress.missions[mission.id] = 0;
	});

	const progressRef = doc(db, 'users', userId, 'missionProgress', todayKey);
	await setDoc(progressRef, initialProgress);
	
	return initialProgress;
}

/**
 * Check if all daily missions are complete
 */
export function checkAllMissionsComplete(progress: DailyMissionProgress): boolean {
	const dailyMissions = getDailyMissions();
	
	return dailyMissions.every(mission => {
		const currentProgress = progress.missions[mission.id] || 0;
		return currentProgress >= mission.target;
	});
}

/**
 * Migrate existing progress document to include new missions
 */
export async function migrateMissionProgress(userId: string, progress: DailyMissionProgress): Promise<DailyMissionProgress> {
	const dailyMissions = getDailyMissions();
	let needsUpdate = false;

	// Add any missing missions with 0 progress
	dailyMissions.forEach(mission => {
		if (!(mission.id in progress.missions)) {
			progress.missions[mission.id] = 0;
			needsUpdate = true;
		}
	});

	// Recalculate completion status
	const allComplete = checkAllMissionsComplete(progress);
	if (progress.completed !== allComplete) {
		progress.completed = allComplete;
		if (allComplete && !progress.completedAt) {
			progress.completedAt = new Date();
		} else if (!allComplete && progress.completedAt) {
			// Remove completedAt field when missions are no longer complete
			delete progress.completedAt;
		}
		needsUpdate = true;
	}

	// Update document if needed
	if (needsUpdate) {
		const todayKey = getTodayKey();
		const progressRef = doc(db, 'users', userId, 'missionProgress', todayKey);
		await setDoc(progressRef, progress);
	}

	return progress;
}

/**
 * Increment mission progress for a specific metric
 */
export async function incrementMissionProgress(
	userId: string,
	metric: MissionMetric,
	increment: number = 1
): Promise<void> {
	const todayKey = getTodayKey();

	const progressRef = doc(db, 'users', userId, 'missionProgress', todayKey);

	try {
		await runTransaction(db, async (transaction) => {
			// Get current progress
			const progressDoc = await transaction.get(progressRef);
			let progress: DailyMissionProgress;
			let isNewDocument = false;

			if (!progressDoc.exists()) {
				// Initialize if doesn't exist - create within transaction to avoid race condition
				const dailyMissions = getDailyMissions();
				progress = {
					missions: {},
					completed: false,
					createdAt: new Date()
				};

				// Initialize all daily missions with 0 progress
				dailyMissions.forEach(mission => {
					progress.missions[mission.id] = 0;
				});
				isNewDocument = true;
			} else {
				progress = progressDoc.data() as DailyMissionProgress;
			}

			// Find missions that use this metric
			const relevantMissions = MISSION_CATALOG.filter(mission => mission.metric === metric);
			let progressUpdated = false;

			// Update progress for relevant missions
			relevantMissions.forEach(mission => {
				const currentProgress = progress.missions[mission.id] || 0;
				const newProgress = Math.min(currentProgress + increment, mission.target);
				
				if (newProgress !== currentProgress) {
					progress.missions[mission.id] = newProgress;
					progressUpdated = true;

					// Track mission completion if just completed
					if (newProgress >= mission.target && currentProgress < mission.target && browser && posthog) {
						posthog.capture('mission_completed', {
							mission_id: mission.id,
							mission_name: mission.name,
							target_value: mission.target,
							completion_time: new Date().toISOString()
						});
					}
				}
			});

			// Check if all missions are now complete
			const allComplete = checkAllMissionsComplete(progress);
			
			if (allComplete && !progress.completed) {
				progress.completed = true;
				progress.completedAt = new Date();

				// Update streak data in separate document
				const streakRef = doc(db, 'users', userId, 'missionProgress', 'streakData');
				const streakDoc = await transaction.get(streakRef);

				const streakData: UserStreakData = streakDoc.exists()
					? streakDoc.data() as UserStreakData
					: {
						currentStreak: 0,
						bestStreak: 0
					};

				// Calculate new streak
				const previousStreak = streakData.currentStreak;
				const newStreak = streakData.lastMissionDate === getTodayKey()
					? streakData.currentStreak
					: streakData.currentStreak + 1;

				const updatedStreakData: UserStreakData = {
					currentStreak: newStreak,
					bestStreak: Math.max(newStreak, streakData.bestStreak),
					lastMissionDate: todayKey
				};

				// Track streak milestone if reached and PostHog is available
				if (browser && posthog && newStreak > previousStreak) {
					const milestones = [3, 7, 14, 30, 50, 100];
					const newMilestone = milestones.find(
					milestone => newStreak >= milestone && previousStreak < milestone
					);
		
					if (newMilestone) {
					posthog.capture('streak_milestone_reached', {
						milestone: newMilestone,
						previous_streak: previousStreak
					});
					}
				}
      
				// Update user properties for PostHog
				if (browser && posthog) {
					posthog.setPersonProperties({
						current_streak: newStreak,
						best_streak: updatedStreakData.bestStreak,
						total_missions_completed: Object.values(progress.missions).reduce((sum, val) => sum + val, 0)
					});
				}

				transaction.set(streakRef, updatedStreakData);
			}

			// Update progress document
			if (isNewDocument || progressUpdated || allComplete) {
				transaction.set(progressRef, progress);
			}
		});
	} catch (error) {
		console.error('Error incrementing mission progress:', error);
		throw error;
	}
}