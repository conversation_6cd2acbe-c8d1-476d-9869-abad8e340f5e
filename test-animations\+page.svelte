<script lang="ts">
	import { notifications } from '$lib/notifications/index.js';
	import ProgressBar from '$lib/analysis/ProgressBar.svelte';
	import { Button, H2, H3, P2 } from '$lib/ui/index.js';

	let progressValue = $state(0);
	let streakValue = $state(0);

	function testMissionComplete() {
		notifications.addMissionComplete('Daily Questions', 'daily_questions');
	}

	function testStreakUpdate() {
		streakValue += 1;
		notifications.addStreakUpdate(streakValue);
	}

	function testAllMissionsComplete() {
		notifications.addAllMissionsComplete();
	}

	function increaseProgress() {
		progressValue = Math.min(progressValue + 20, 100);
	}

	function resetProgress() {
		progressValue = 0;
	}

	function clearNotifications() {
		notifications.clear();
	}
</script>

<div class="test-container">
	<H2>Animation Test Page</H2>
	<P2>Test the new user feedback animations</P2>

	<div class="test-section">
		<H3>Notification Tests</H3>
		<div class="button-group">
			<Button onclick={testMissionComplete}>Test Mission Complete</Button>
			<Button onclick={testStreakUpdate}>Test Streak Update ({streakValue})</Button>
			<Button onclick={testAllMissionsComplete}>Test All Missions Complete</Button>
			<Button onclick={clearNotifications} isSecondary={true}>Clear Notifications</Button>
		</div>
	</div>

	<div class="test-section">
		<H3>Progress Bar Animation Test</H3>
		<div class="progress-demo">
			<P2>Progress: {progressValue}%</P2>
			<ProgressBar 
				percentage={progressValue}
				--height="2rem"
				--light-color="var(--light-sky-blue)"
				--color="var(--sky-blue)"
			/>
		</div>
		<div class="button-group">
			<Button onclick={increaseProgress}>Increase Progress (+20%)</Button>
			<Button onclick={resetProgress} isSecondary={true}>Reset Progress</Button>
		</div>
	</div>

	<div class="test-section">
		<H3>Instructions</H3>
		<ul>
			<li>Click "Test Mission Complete" to see the slide-down notification</li>
			<li>Click "Test Streak Update" to see streak notifications with different messages</li>
			<li>Click "Test All Missions Complete" to see the celebration notification</li>
			<li>Click "Increase Progress" to see smooth progress bar animation</li>
			<li>Watch for completion flash when progress reaches 100%</li>
		</ul>
	</div>
</div>

<style>
	.test-container {
		max-width: 800px;
		margin: 0 auto;
		padding: 2rem;
		display: flex;
		flex-direction: column;
		gap: 2rem;
	}

	.test-section {
		padding: 1.5rem;
		border: 1px solid var(--pitch-black);
		border-radius: 0.75rem;
		background: var(--white);
	}

	.button-group {
		display: flex;
		gap: 1rem;
		flex-wrap: wrap;
		margin-top: 1rem;
	}

	.progress-demo {
		margin: 1rem 0;
		padding: 1rem;
		background: var(--very-light-sky-blue);
		border-radius: 0.5rem;
	}

	ul {
		margin-left: 1.5rem;
		margin-top: 1rem;
	}

	li {
		margin-bottom: 0.5rem;
		font-family: "Open Sans", sans-serif;
		font-size: 1rem;
		line-height: 1.5;
	}

	@media (max-width: 768px) {
		.test-container {
			padding: 1rem;
		}

		.button-group {
			flex-direction: column;
		}
	}
</style>
