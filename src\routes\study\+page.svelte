<script lang="ts">
    import H2 from '$lib/ui/typography/H2.svelte';
    import QuestionBankProgress from '$lib/dashboard/QuestionBankProgress.svelte';
    import DailyMissions from '$lib/dashboard/DailyMissions.svelte';
    import StreakBadge from '$lib/dashboard/StreakBadge.svelte';
    import { getAuthContext } from '$lib/firebase/auth.svelte.ts';

    const { user, loading } = getAuthContext();

    // $inspect(authContext);
    // $inspect(user);
</script>

<svelte:head>
    <title>Study - DSAT16</title>
</svelte:head>

<div class="dashboard-container">
    <H2>Welcome{user ? `, ${user.displayName}` : ""}!</H2>
    <section class="dashboard-grid">
        <!-- Top row: Question Bank Progress (2/3) + Streak Badge (1/3) -->
        <div class="top-row">
            <div class="question-bank-card">
                <QuestionBankProgress />
            </div>
            <div class="streak-card">
                <StreakBadge />
            </div>
        </div>

        <!-- Bottom row: Daily Missions (full width) -->
        <div class="bottom-row">
            <div class="daily-missions-card">
                <DailyMissions />
            </div>
        </div>
    </section>
</div>



<style>
    .dashboard-container {
        display: flex;
        gap: 1.25rem;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        padding: 4rem;
    }

    .dashboard-grid {
        display: flex;
        flex-direction: column;
        gap: 1.25rem;
        width: 100%;
        max-width: 80rem;
    }

    .top-row {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 1.25rem;
        width: 100%;
    }

    .bottom-row {
        width: 100%;
    }

    .question-bank-card,
    .streak-card,
    .daily-missions-card {
        border-radius: 0.75rem;
        border: 0.125rem solid var(--pitch-black);
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        padding: 1.25rem;
        height: 100%;
        width: 100%;
    }

    .question-bank-card {
        background: var(--very-light-sky-blue, #f5fdff);
    }

    .streak-card {
        background: var(--light-tangerine, #FDE2C5);
    }

    .daily-missions-card {
        background: var(--light-yellow, #FFF1C1);
    }

    @media (max-width: 960px) {
        .dashboard-container {
            padding: 1rem;
        }

        .top-row {
            grid-template-columns: 1fr;
        }

        .question-bank-card,
        .streak-card,
        .daily-missions-card {
            max-width: 100%;
        }
    }

    @media (min-width: 1024px) and (max-width: 1200px) {
        .dashboard-container {
            padding: 4rem 2rem;
        }
    }
</style>